* {
	padding: 0;
	margin: 0;
	text-decoration: none;
	list-style: none;
}

body {

	width: 100%;
	font-family: Maax-Medium, "Helvetica Neue", Helvetica, Arial, sans-serif;


}

a {
	text-decoration: none;
	color: #000;
	display: block;
}

img {
	object-fit: cover;
}

/* 头部广告位 */
.de_iv_adv {
	width: 100%;
	height: auto;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 5px 0;
	/* background-color: #F8F8F8; */
	margin: 10px 0 0px;
}

/* 底部广告位 */
.postionFixed {
	width: 100%;
	min-height: 60px;
	position: fixed;
	display: flex;
	justify-content: center;
	align-items: center;
	background-color: #F8F8F8;
	right: 0;
	left: 0;
	bottom: 0;
	z-index: 9999;
}

.footer {
	width: 100%;
	height: auto;
	text-align: center;
	padding: 10px 0 10px;
	font-size: 15px;
	color: #FFFFFF;
	background-color: #395c7a;
	color: #FFFFFF;
	margin-top: 10px;
	margin-bottom: 60px;
	line-height: 1.5;
	font-family: "times new roman";
}

.about {
	width: 100%;
	display: flex;
	margin: 0 auto;
	justify-content: center;
}

.about>a {
	text-decoration: none;
	color: #FFFFFF;
	text-align: center;
	padding: 0px 5px;
}

.ad {
	background-color: #FFFFFF;
	border-top: 1px solid #FFFFFF;

}

.adv {
	text-align: center;
	color: gray;
	font-size: 13px;
	line-height: 1.5;
	border-bottom: 1px solid gainsboro;
	width: 100%;

}

@media (max-width: 1000px) {

	.header {
		width: 100%;
		height: auto;
		position: relative;
		box-shadow: 0px 0px 10px gray;
		/* border-bottom: 1px solid #075277; */
		background-color: #fff;
		padding: 5px 0 0;
		border-top: 10px solid #395c7a;
		position: sticky;
		top: 0;
		z-index: 99999;

	}

	.header-box {
		width: 96%;
		height: 60px;
		margin: 0 auto;
		display: flex;
		padding: 0 2%;
		justify-content: space-around;
		align-items: center;
		box-sizing: border-box;
	}

	.h-title {
		height: 50px;

	}

	.h-title img {
		width: 80%;
		height: 100%;
		object-fit: contain;
		display: block;
		margin: auto;
	}

	.h2-box {
		width: 100%;
		background-color: #FFFFFF;
	}

	.menu {
		width: 6%;
		height: 37px;
		display: block;
	}

	#del {
		width: 7%;
		height: 25px;
		position: absolute;
		top: 33px;
		left: 4%;
		display: none;
	}

	.search-img {
		width: 9%;
		height: 35px;
	}

	.search-img2 {
		display: none;
	}

	.menu_list {
		display: flex;
		justify-content: space-around;
		width: 100%;
		height: auto;
		padding: 10px 0 10px 0;
		flex-wrap: wrap;
		align-content: flex-start;
		/* border-top: 1px solid gainsboro; */
		background-color: #FFFFFF;
		display: none;
		position: absolute;
		top: 60px;
		height: calc(100vh - 60px);

	}

	.menu_list a {
		width: 90%;
		display: block;
		font-size: 18px;
		/* font-weight: 600; */
		line-height: 2.5;
		text-decoration: none;
		border-bottom: 1px solid gainsboro;
		/* font-family: "times new roman"; */
		color: #333;
	}


	/* .menu_list a:nth-last-of-type(1) {
		border: none;
	}
 */
	.seachdiv {
		width: 100%;
		height: 40px;
		margin: 0 auto;
		background-color: #FFFFFF;
		display: flex;
		justify-content: center;
		/* border-top: 2px solid  #9761a0 ; */
		padding: 10px 0;
		position: relative;
		display: none;
		position: absolute;
		top: 60px;
		height: calc(100vh - 60px);
	}

	.ts {
		width: 0;
		height: 0;
		border-right: 10px solid transparent;
		border-left: 10px solid transparent;
		border-bottom: 10px solid #9761a0;
		position: absolute;
		top: -10px;
		right: 10px;
		display: none;

	}

	#seach {
		display: block;
		border: none;
		outline: 0;
		cursor: pointer;
		width: 80%;
		height: 40px;
		font-size: 14px;
		margin: 20px 0 0;
		text-indent: 1em;
		background-color: #FFFFFF;
		border: 1px solid gainsboro;
	}

	.seach-img {
		width: 15%;
		height: 40px;
		margin: 20px 0 0;
		background-color: #000000;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.seach-img img {
		width: 30px;
		height: 30px;

	}

	.logo {
		width: 85%;
		height: 110px;
		margin: 15px auto;
	}

	.logo img {
		width: 100%;
		height: 100%;
	}

	.web_title {
		width: 100%;
		height: auto;
		background-color: #fac331;
		background-image: url(../images/bg.webp);
		/* margin-bottom: 130px; */
		background-size: 292px auto;
		padding: 10px 0;

	}

	.wt_box {
		width: 90%;
		height: auto;
		margin: 0 auto;
	}

	.wt_title {
		text-align: center;
		line-height: 1.5;
		font-size: 30px;
		font-weight: 600;

	}

	.wt_text {
		width: 90%;
		margin: 0 auto;
		font-size: 16px;
		color: #000000;
		line-height: 1.5;
		text-align: center;
		padding: 10px 0;
	}

	.web_title img {
		width: 90%;
		height: 200px;
		display: block;
		margin: 0 auto;
		box-shadow: 0px 0px 10px gray;
	}


}

@media (min-width: 1000px) {
	.header {
		width: 100%;
		height: auto;
		/* position: relative; */
		background-color: #FFFFFF;
		margin: auto;
		box-shadow: 0px 0px 10px gainsboro;
		border-top: 30px solid #395c7a;
	}

	.h-box {
		width: 75%;
		height: auto;
		margin: 0 auto;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-around;
		align-items: center;
		position: relative;
		/* background-color: pink; */
	}

	.header-box {
		width: 15%;
		/* background-color: #000000; */
	}

	#menu {
		display: none;
	}

	.search-img {
		display: none;
	}

	.h-title {
		font-size: 25px;
		color: #000000;
		font-weight: 600;
		text-align: center;
	}

	.h-title img {
		width: 100%;
		height: 100%;
		vertical-align: middle;
	}

	.h2-box {
		width: 80%;
		height: 70px;
		/* padding: 10px 0; */
		margin: 0 auto;
		display: flex;
		justify-content: space-around;
		align-items: center;
	}

	.search-img {
		width: 7%;
		height: 25px;
	}

	#del {
		display: none !important;
	}

	.search-img2 {
		width: 35px;
		height: 35px;
	}

	.menu_list {
		display: block !important;
		display: flex !important;
		justify-content: space-around;
		align-items: center;
		width: 70%;
		height: auto;
		padding: 10px 0 10px 0;
		/* background-color: pink; */

	}

	.menu_list a {
		width: auto;
		line-height: 40px;
		display: block;
		font-size: 18px;
		/* font-weight: 600; */
		text-align: center;
		text-decoration: none;
		/* font-family: "times new roman"; */
		padding: 0 10px;
		color: #000000;
	}

	.menu_list a:hover {
		color: #FFFFFF;
		background-color: #767676;
	}

	.seachdiv {
		width: 350px;
		height: 40px;
		background-color: #FFFFFF;
		display: flex;
		justify-content: center;
		align-items: center;
		border: 1px solid gainsboro;
		border-top: 3px solid #9761a0;
		padding: 15px 10px;
		display: none;
		position: absolute;
		top: 60px;
		right: 7%;
		z-index: 999;


	}

	.ts {
		width: 0;
		height: 0;
		border-right: 10px solid transparent;
		border-left: 10px solid transparent;
		border-bottom: 10px solid #9761a0;
		position: absolute;
		top: -10px;
		right: 20px;

	}

	#seach {
		display: block;
		border: none;
		outline: 0;
		cursor: pointer;
		height: 100%;
		width: 80%;
		font-size: 14px;
		/* margin: 0 auto; */
		text-indent: 2em;
		background-color: #FFFFFF;
		border: 1px solid gainsboro;
	}

	.seach-img {
		width: 20%;
		height: 100%;
		background-color: #767676;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.seach-img img {
		width: 30px;
		height: 30px;

	}



}