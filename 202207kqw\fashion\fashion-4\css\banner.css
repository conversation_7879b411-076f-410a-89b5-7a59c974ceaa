@media (max-width:1000px) {
	.banner{
		width: 100%;
		padding: 20px 0;
		background-color: #000000;
	}
	.b-box{
		width: 100%;
		height: auto;
		position: relative;
		margin-bottom: 5px;
	}
	.b-box img{
		width: 100%;
		height: 200px;
		display: block;
	}
	.b-text{
		width: 90%;
		height: auto;
		position: absolute;
		top: 100px;
		left: 0;
		right: 0;
		margin: auto;
		color: #FFFFFF;
		z-index: 2;
	}
	.b-text p:nth-of-type(1){
		width: auto;
		height: auto;
		line-height: 1.6;
		padding: 5px 10px;
		background-color: #395c7a;
		font-size: 13px ;
		text-transform: uppercase;
		display: inline-block;
		zoom:1;
		
	}
	.b-text p:nth-of-type(2){
		font-size: 16px;
		font-weight: 600;
		line-height: 1.5;
		margin-top: 10px;
		
	}
	.b-mb{
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		background-color: rgba(0,0,0,0.3);
		z-index: 1;
	}
}
/* PC */
@media (min-width:1000px) {
	.banner{
		width: 1300px;
		margin: 10px  auto;
		display: flex;
		justify-content: space-around;
		background-color: #000000;
		/* padding: 15px 0; */
		
	}
	.b-box{
		width: 24.5%;
		height: auto;
		height: 350px;
		position: relative;
		overflow: hidden;
		/* margin-bottom: 5px; */
	
	}
	.b-box img{
		width: 100%;
		height: 100%;
		display: block;
		transition: all 0.5s linear;
		transform: scale(1.0);
	}
	.b-box:hover img{
		transform: scale(1.15);
	}
	.b-box:hover .b-mb{
		background-color: rgba(0,0,0,0.2);
	}
	.b-text{
		width: 90%;
		height: auto;
		position: absolute;
		top: 200px;
		left: 0;
		right: 0;
		margin: auto;
		color: #FFFFFF;
		z-index: 2;
	}
	.b-text p:nth-of-type(1){
		width: auto;
		height: auto;
		line-height: 1.6;
		padding: 5px 10px;
		background-color: #395c7a;
		font-size: 14px ;
		text-transform: uppercase;
		display: inline-block;
		zoom:1;
		font-weight: 600;
		
	}
	.b-text p:nth-of-type(2){
		font-size: 20px;
		font-weight: 600;
		line-height: 1.5;
		margin-top: 10px;
		
	}
	.b-mb{
		width: 100%;
		height: 100%;
		position: absolute;
		top: 0;
		background-color: rgba(0,0,0,0.3);
		z-index: 1;
	}
}