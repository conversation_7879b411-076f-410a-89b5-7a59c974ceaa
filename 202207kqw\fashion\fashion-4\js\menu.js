var menuAll = document.querySelectorAll(".menu_list a")
var windwith = window.innerWidth

function $(str) {
	return document.querySelector(str)
}

let state = 0
let state2 = 0
$(".menu").onclick = () => {
	if (state == 0) {
		$('.menu_list').style.display = "flex"
		$('.seachdiv').style.display = "none"
		document.documentElement.style.overflowY = "hidden"
		if (!$(".article-box")) {
			$(".menu").src = "images/delete.png"
			$(".search-img").src = "images/seach.png"
		} else {
			$(".menu").src = "../images/delete.png"
			$(".search-img").src = "../images/seach.png"
		}
		$(".main").style.display = "none"
		state = 1
		state2 = 0
	} else {
		$('.menu_list').style.display = "none"
		document.documentElement.style.overflowY = "auto"
		$(".main").style.display = "block"
		if (!$(".article-box")) {
			$(".menu").src = "images/menu.png"
		} else {
			$(".menu").src = "../images/menu.png"
		}
		state = 0
	}
}

$(".search-img").onclick = () => {
	if (state2 == 0) {
		$('.seachdiv').style.display = "flex"
		$(".main").style.display = "none"
		document.documentElement.style.overflowY = "hidden"
		if (!$(".article-box")) {
			$(".search-img").src = "images/delete.png"
			$(".menu").src = "images/menu.png"
		} else {
			$(".search-img").src = "../images/delete.png"
			$(".menu").src = "../images/menu.png"
		}
		$('.menu_list').style.display = "none"
		state = 0
		state2 = 1
	} else {
		$('.seachdiv').style.display = "none"
		$(".main").style.display = "block"
		document.documentElement.style.overflowY = "auto"
		if (!$(".article-box")) {
			$(".search-img").src = "images/seach.png"
		} else {
			$(".search-img").src = "../images/seach.png"
		}
		state2 = 0
	}
}
var state3 = 0
$(".search-img2").onclick = function() {
	// console.log(11111)
	if (state3 == 0) {
		seachdiv.style.display = 'flex'
		state3 = 1
	} else {
		seachdiv.style.display = 'none'
		state3 = 0
	}
}

function menuAction() {
	if (windwith < 960) {
		for (let i in menuAll) {
			menuAll[i].onclick = () => {
				document.documentElement.style.overflowY = "auto"
				$('.menu_list').style.display = "none"
				$(".menu").src = "images/menu.png"
				$(".main").style.display = "block"
				state = 0
			}
		}
	}
}
menuAction()
