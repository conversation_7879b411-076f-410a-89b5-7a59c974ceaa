#link4{
	border-bottom: 2px solid #68bb6d;
}
#link4 .m-text{
	
	background-color: #68bb6d;
}
#link6{
	border-bottom: 2px solid #e71a64;
}
#link6 .m-text{
	
	background-color: #e71a64;
}
@media (max-width:1000px) {
	.main {
		width: 100%;
		height: auto;
		padding: 15px 0;
		font-family: Abril-Fatface-Regular;
		margin: 0 auto;
	}

	.m-title {
		width: 90%;
		height: auto;
		margin: auto;
		border-bottom: 2px solid #395c7a;
		display: flex;
		justify-content: space-between;
	}
	.explore{
		color: #3a5c79;
		font-size: 16px;
	}
	.m-text {
		width: auto;
		display: inline-block;
		height: auto;
		zoom: 1;
		text-align: center;
		line-height: 1.5;
		padding: 0px 10px;
		color: #FFFFFF;
		background-color: #395c7a;
		font-size: 18px;

	}

	.m-ts {
		width: auto;
		height: auto;
		background-color: #000000;
		display: inline-block;
		font-size: 15px;
		color: #FFFFFF;
		padding: 2px 5px;
		line-height: 1.5;
		zoom: 1;
		position: absolute;
		bottom: 0;
		left: 0;
	}

	.list1 {
		width: 90%;
		height: auto;
		padding: 25px 0;
		margin: 0 auto;
		position: relative;
	}

	.l1-box:nth-of-type(1) {
		width: 100%;
		height: auto;
		display: block !important;

	}

	.l1-box img {
		width: 100%;
		height: 100%;
	}

	.l1-box:nth-of-type(1) .l1-img {
		width: 100%;
		height: 220px;
		position: relative;

	}

	.l1-box:nth-of-type(1) .l1-text {
		width: 100%;
		height: auto;
		padding: 10px 0;
	}

	.l1-box:nth-of-type(1) .l1-text p:nth-of-type(1) {
		font-size: 18px;
		line-height: 1.5;
		font-weight: 600;
	}

	.l1-box:nth-of-type(1) .l1-text p:nth-of-type(2) {
		font-size: 15px;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		color: gray;
		margin: 10px 0 0;
	}

	.l1-box {
		width: 100%;
		display: flex;
		justify-content: space-between;
		margin-bottom: 25px;
		/* align-items: center; */
	}

	.l1-box .l1-img {
		width: 35%;
		height: 90px;
		display: block;
	}

	.l1-text {
		width: 60%;
		height: auto;

	}

	.l1-text p:nth-of-type(1) {
		font-size: 15px;
		line-height: 1.5;
		font-weight: 600;
	}

	.l1-text p:nth-of-type(2) {
		font-size: 13px;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		color: gray;
		margin: 10px 0 0;
	}

	.list2 {
		width: 90%;
		height: auto;
		padding: 25px 0;
		margin: 0 auto;
	}

	.l2-box {
		width: 100%;
		height: auto;
		margin-bottom: 25px;
	}

	.l2-img {
		width: 100%;
		height: 200px;
		position: relative;

	}

	.l2-img img {
		width: 100%;
		height: 100%;
	}

	.l2-text {
		font-size: 18px;
		font-weight: 600;
		line-height: 1.6;
		padding: 10px 0;
	}

	.list3 {
		width: 90%;
		height: auto;
		padding: 20px 0;
		margin: 0 auto;
	}

	.l3-box:nth-of-type(1),
	.l3-box:nth-of-type(2) {
		width: 100%;
		height: 240px;
		margin-bottom: 25px;
		position: relative;
		display: block !important;
	}

	.l3-box:nth-of-type(1) img,
	.l3-box:nth-of-type(2) img {
		width: 100%;
		height: 100%;
	}

	.l3-mb {
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.4);
		position: absolute;
		top: 0;
		z-index: 1;
	}

	.l3-box:nth-of-type(1) .l3-text,
	.l3-box:nth-of-type(2) .l3-text {
		width: 90%;
		height: auto;
		position: absolute;
		top: 130px;
		left: 0;
		right: 0;
		margin: auto;
		z-index: 2;
	}

	.l3-box:nth-of-type(1) .l3-text p:nth-of-type(1),
	.l3-box:nth-of-type(2) .l3-text p:nth-of-type(1) {
		font-size: 18px;
		font-weight: 600;
		line-height: 1.5;
		color: #FFFFFF;
	}

	.l3-box:nth-of-type(1) .l3-text span,
	.l3-box:nth-of-type(2) .l3-text span {
		background-color: #FFFFFF;
		color: #000000;
		padding: 1px 3px;
		font-size: 14px;
		margin-right: 5px;
	}

	.l3-box:nth-of-type(1) .l3-text p:nth-of-type(2),
	.l3-box:nth-of-type(2) .l3-text p:nth-of-type(2) {
		font-size: 13px;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		color: gainsboro;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		margin-top: 8px;
	}

	.l3-box {
		width: 100%;
		height: auto;
		display: flex;
		justify-content: space-between;
		margin-bottom: 20px;

	}

	.l3-box img {
		width: 35%;
		height: 90px;
	}

	.l3-text {
		width: 60%;
		height: auto;
	}

	.l3-text p:nth-of-type(1) {
		font-size: 16px;
		font-weight: 600;
		line-height: 1.5;
	}

	.l3-text p:nth-of-type(2) {
		font-size: 13px;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		color: gray;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		margin-top: 8px;
	}

	.l3-text span {
		padding: 1px 3px;
		font-size: 14px;
		margin-right: 5px;
		background-color: #000000;
		color: #FFFFFF;
	}
	.list4 {
		width: 90%;
		height: auto;
		padding: 15px 0;
		margin: 0 auto;
	}
	.l4-box{
		width: 100%;
		height: auto;
		margin-bottom: 20px;
	}
	.l4-box img{
		width: 100%;
		height: 200px;
	}
	.l4-text{
		width: 100%;
		height: auto;
		padding: 15px 0;
	}
	.l4-text p:nth-of-type(1){
		font-size: 18px;
		line-height: 1.5;
		font-weight: 600;
		
	}
	.l4-text p:nth-of-type(2){
		width: auto;
		height: auto;
		line-height: 1.5;
		font-size: 15px;
		background-color: #000000;
		color: #FFFFFF;
		padding: 2px 5px;
		margin-top: 10px;
		display: inline-block;
	}
	.l4-text p:nth-of-type(3){
		font-size: 15px;
		line-height: 1.5;
		color: gray;
		overflow: hidden;
		text-overflow: ellipsis;
		color: gray;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		margin-top: 8px;
	}
	.btn{
		display: none;
	}
	.list5 {
		width: 90%;
		height: auto;
		padding: 20px 0;
		margin: 0 auto;

	}

	.l5-box {
		width: 100%;
		height: auto;
		display: flex;
		justify-content: space-between;
		margin-bottom: 20px;
	}

	.l5-box img {
		width: 35%;
		height: 100px;

	}

	.l5-text {
		width: 62%;
		height: auto;
	}

	.l5-text p:nth-of-type(1) {
		font-size: 16px;
		font-weight: 600;
		line-height: 1.5;
	}

	.l5-text p:nth-of-type(2) {
		font-size: 13px;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		color: gray;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		margin-top: 8px;
	}

	.l5-text span {
		padding: 1px 3px;
		font-size: 14px;
		margin-right: 5px;
		font-weight: 600;
		color: #FFFFFF;
		background-color: #3a5c79;
	}
	.list6{
		width: 90%;
		height: auto;
		padding: 15px 0;
		margin: 0 auto;
	}
	.l6-box:nth-of-type(1){
		width: 100%;
		height: auto;
		margin-bottom: 20px;
		border: 0;
	}
	.l6-box:nth-of-type(1) .l6-img{
		width: 100%;
		height: 200px;
		position: relative;
	}
	.l6-img img{
		width: 100%;
		height: 100%;
	}
	.l6-box:nth-of-type(1) .l6-text{
		width: 100%;
		height:auto;
		padding: 10px 0;
	}
	.l6-box:nth-of-type(1) .l6-text p:nth-of-type(1){
		font-size: 18px;
		font-weight: 600;
		line-height: 1.6;
	}
	.l6-box:nth-of-type(1) .l6-text p:nth-of-type(2){
		background-color: #e71a64;
		width: auto;
		height: auto;
		display: inline-block;
		color: #FFFFFF;
		font-size: 15px;
		padding: 2px 5px;
		margin: 5px 0 0;
		font-weight: 400;
	}
	.l6-box:nth-of-type(1) .l6-text p:nth-of-type(3){
		font-size: 15px;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		color: gray;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		margin-top: 8px;
		font-weight: 400;
	}
	.l6-box{
		width: 100%;
		height: auto;
		padding: 10px 0 15px;
		line-height: 1.5;
		font-weight: 600;
		font-size: 18px;
		border-bottom: 1px dashed gray;
		margin-bottom: 20px;
	}
	
}

@media (min-width:1000px) {
	.main {
		width: 1300px;
		height: auto;
		padding: 15px 0;
		margin: 0 auto;
	}

	.m-title {
		width: 100%;
		height: auto;
		margin: auto;
		border-bottom: 2px solid #395c7a;
		display: flex;
		justify-content: space-between;
		
	}
	.explore{
		color: #3a5c79;
		font-size: 18px;
	}
	.m-text {
		width: auto;
		display: inline-block;
		height: auto;
		zoom: 1;
		text-align: center;
		line-height: 2;
		padding: 0px 10px;
		color: #FFFFFF;
		background-color: #395c7a;
		font-size: 18px;
	}

	.m-ts {
		width: auto;
		height: auto;
		background-color: #000000;
		display: inline-block;
		font-size: 14px;
		color: #FFFFFF;
		padding: 0px 5px;
		line-height: 1.5;
		zoom: 1;
		position: absolute;
		bottom: 0;
		left: 0;
	}
	.m-ts:hover{
		background-color: #395c7a;
	}
	.list1 {
		width: 100%;
		min-height: 400px;
		padding: 20px 0;
		margin: 0 auto;
		height: 377px;
		display: flex;
		flex-direction: column;
		flex-wrap: wrap;
		/* background-color: pink; */
	}

	.l1-box img {
		width: 100%;
		height: 100%;
	}
	.l1-box:hover .l1-text p:nth-of-type(1){
		color: #526fa9;
	}
	.l1-box:nth-of-type(1) {
		width: 28%;
		height: auto;
		display: block !important;
		margin: 0 0 20px 10px;


	}

	.l1-box:nth-of-type(1) .l1-img {
		width: 100%;
		height: 260px;
		position: relative;
	}

	.l1-box:nth-of-type(1) .l1-text {
		width: 100%;
		height: auto;
		padding: 10px 0;
	}

	.l1-box:nth-of-type(1) .l1-text p:nth-of-type(1) {
		font-size: 18px;
		line-height: 1.5;
		font-weight: 600;
	}

	.l1-box:nth-of-type(1) .l1-text p:nth-of-type(2) {
		font-size: 15px;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 3;
		color: gray;
		margin: 10px 0 0;
	}

	.l1-box {
		width: 21%;
		height: auto;
		display: flex;
		justify-content: space-between;

		margin: 0 10px 25px 10px;
	}

	.l1-box .l1-img {
		width: 35%;
		height: 90px;
		position: relative;
	}

	.l1-text {
		width: 61%;
		height: auto;

	}

	.l1-text p:nth-of-type(1) {
		font-size: 15px;
		line-height: 1.5;
		font-weight: 600;
	}

	.l1-text p:nth-of-type(2) {
		font-size: 13px;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		color: gray;
		margin: 10px 0 0;
	}

	.flex-box {
		width: 100%;
		height: auto;
		margin: 20px 0;
		display: flex;
		justify-content: space-between;
	}

	.fb-left {
		width: 68%;
	}

	.fb-right {
		width: 26%;
		position: relative;
	}

	.list2 {
		width: 100%;
		height: auto;
		padding: 25px 0;
		margin: 0 auto;
		display: flex;
		flex-wrap: wrap;
		/* background-color: pink; */

	}

	.l2-box {
		width: 30%;
		height: auto;
		margin: 0 10px 25px 17px;
	}

	.l2-img {
		width: 100%;
		height: 210px;
		position: relative;

	}

	.l2-img img {
		width: 100%;
		height: 100%;
	}
	.l2-box:hover .l2-text{
		color: #526fa9;
	}
	.l2-text {
		font-size: 18px;
		font-weight: 600;
		line-height: 1.6;
		padding: 10px 0;
	}

	.list3 {
		width: 100%;
		height: auto;
		padding: 20px 0;
		margin: 0 auto;
		display: flex;
		/* background-color: pink; */
		flex-wrap: wrap;
	}

	.l3-box:nth-of-type(1),
	.l3-box:nth-of-type(2) {
		width: 46%;
		height: 300px;
		margin: 0 10px 25px 20px;
		position: relative;
		display: block !important;
	}

	.l3-box:nth-of-type(1) img,
	.l3-box:nth-of-type(2) img {
		width: 100%;
		height: 100%;
	}

	.l3-mb {
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.4);
		position: absolute;
		top: 0;
		z-index: 1;
	}

	.l3-box:nth-of-type(1) .l3-text,
	.l3-box:nth-of-type(2) .l3-text {
		width: 90%;
		height: auto;
		position: absolute;
		top: 170px;
		left: 0;
		right: 0;
		margin: auto;
		z-index: 2;
	}

	.l3-box:nth-of-type(1) .l3-text p:nth-of-type(1),
	.l3-box:nth-of-type(2) .l3-text p:nth-of-type(1) {
		font-size: 18px;
		font-weight: 600;
		line-height: 1.5;
		color: #FFFFFF;
	}

	.l3-box:nth-of-type(1) .l3-text span,
	.l3-box:nth-of-type(2) .l3-text span {
		background-color: #FFFFFF;
		color: #000000;
		padding: 1px 3px;
		font-size: 14px;
		margin-right: 5px;
	}

	.l3-box:nth-of-type(1) .l3-text p:nth-of-type(2),
	.l3-box:nth-of-type(2) .l3-text p:nth-of-type(2) {
		font-size: 13px;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		color: gainsboro;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		margin-top: 8px;
	}

	.l3-box {
		width: 46%;
		height: auto;
		display: flex;
		justify-content: space-between;
		margin: 0 10px 25px 20px;

	}

	.l3-box img {
		width: 30%;
		height: 90px;
	}

	.l3-text {
		width: 65%;
		height: auto;
	}
	.l3-box:hover .l3-text p:nth-of-type(1){
		color: #395c7a;
	}
	
	.l3-text p:nth-of-type(1) {
		font-size: 16px;
		font-weight: 600;
		line-height: 1.5;
	}

	.l3-text p:nth-of-type(2) {
		font-size: 13px;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		color: gray;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		margin-top: 8px;
	}

	.l3-text span {
		padding: 1px 3px;
		font-size: 14px;
		margin-right: 5px;
		background-color: #000000;
		color: #FFFFFF;
	}
	.list4 {
		width: 100%;
		height: auto;
		padding: 15px 0;
		margin: 0 auto;
		
	}
	.l4-box{
		width: 100%;
		height: auto;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		margin-bottom: 20px;
	}
	.l4-box img{
		width: 35%;
		height: 230px;
	}
	.l4-text{
		width: 62%;
		height: auto;
		/* padding: 15px 0; */
	}
	.btn{
		width: 200px;
		border: 1px solid #6abb70;
		line-height: 45px;
		margin-top: 15px;
		text-align: center;
		font-size: 18px;
	}
	.l4-box:hover .l4-text p:nth-of-type(1){
		color: #6abb70;
	}
	.l5-box:hover .l5-text p:nth-of-type(1){
		color: #3a5c79;
	}
	.l6-box:hover .l6-text p:nth-of-type(1){
		color: #e51665;
	}
	.l6-box:hover{
		color: #e51665;
	}
	.l4-text p:nth-of-type(1){
		font-size: 21px;
		line-height: 1.6;
		font-weight: 600;
		
	}
	.btn:hover{
		background-color: #6abb70;
		color: #FFFFFF;
	}
	.l4-text p:nth-of-type(2){
		width: auto;
		height: auto;
		line-height: 1.5;
		font-size: 15px;
		background-color: #000000;
		color: #FFFFFF;
		padding: 2px 5px;
		margin-top: 10px;
		display: inline-block;
	}
	.l4-text p:nth-of-type(3){
		font-size: 16px;
		line-height: 1.6;
		color: gray;
		/* overflow: hidden;
		text-overflow: ellipsis;
		color: gray;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2; */
		margin-top: 8px;
	}
	.list5 {
		width: 100%;
		height: auto;
		padding: 20px 0;
		margin: 0 auto;
		position: sticky;
		top: 0;
		bottom: auto;

	}

	.l5-box {
		width: 100%;
		height: auto;
		display: flex;
		justify-content: space-between;
		margin-bottom: 20px;
	}

	.l5-box img {
		width: 35%;
		height: 100px;

	}

	.l5-text {
		width: 62%;
		height: auto;
	}

	.l5-text p:nth-of-type(1) {
		font-size: 16px;
		font-weight: 600;
		line-height: 1.5;
	}

	.l5-text p:nth-of-type(2) {
		font-size: 13px;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		color: gray;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		margin-top: 8px;
	}

	.l5-text span {
		padding: 1px 3px;
		font-size: 14px;
		margin-right: 5px;
		font-weight: 600;
		color: #FFFFFF;
		background-color: #3a5c79;
	}
	.list6{
		width: 100%;
		height: auto;
		padding: 15px 0;
		margin: 0 auto;
		
	}
	.l6-box:nth-of-type(1){
		width: 100%;
		height: auto;
		margin-bottom: 20px;
		border: 0;
	}
	.l6-box:nth-of-type(1) .l6-img{
		width: 100%;
		height: 200px;
		position: relative;
	}
	.l6-img img{
		width: 100%;
		height: 100%;
	}
	.l6-box:nth-of-type(1) .l6-text{
		width: 100%;
		height:auto;
		padding: 10px 0;
	}
	.l6-box:nth-of-type(1) .l6-text p:nth-of-type(1){
		font-size: 18px;
		font-weight: 600;
		line-height: 1.6;
	}
	.l6-box:nth-of-type(1) .l6-text p:nth-of-type(2){
		background-color: #e71a64;
		width: auto;
		height: auto;
		display: inline-block;
		color: #FFFFFF;
		font-size: 15px;
		padding: 2px 5px;
		margin: 5px 0 0;
		font-weight: 400;
	}
	.l6-box:nth-of-type(1) .l6-text p:nth-of-type(3){
		font-size: 15px;
		line-height: 1.5;
		overflow: hidden;
		text-overflow: ellipsis;
		color: gray;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		margin-top: 8px;
		font-weight: 400;
	}
	.l6-box{
		width: 100%;
		height: auto;
		padding: 10px 0 15px;
		line-height: 1.5;
		font-weight: 600;
		font-size: 18px;
		border-bottom: 1px dashed gray;
		margin-bottom: 20px;
	}
	
}
