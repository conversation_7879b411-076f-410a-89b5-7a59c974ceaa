function doSeach() {
	var value1 = document.getElementById("seach")
	var value = document.getElementById("seach").value;
	var http = document.getElementById("seach").dataset.http;
	var state = document.getElementById("seach").dataset.state

	if (value && http == 0) {
		window.location.href = "search.html" + "?key=" + value1.value
	} else if (value && http == 1) (

		window.location.href = "../search.html" + "?key=" + value1.value
	)
}

var seach = document.getElementById("seach")
seach.onkeydown = function (event) {
	clickSeach(event)
}
seach.onKeypress = function (event) {
	clickSeach(event)
};

function clickSeach(event) {
	var event = window.event || event;
	var keycode = event.keyCode;
	if (keycode == '13') {
		doSeach()
	}

}

$(".h-title").onclick = () => {
	if ($(".article-box")) {
		window.location.href = "../index.html"
	} else {
		window.location.href = "index.html"
	}
}


