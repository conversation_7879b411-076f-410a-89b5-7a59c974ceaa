.article-box h3 {
		width: 95%;
		
		margin: 0 auto 20px;
		
		
		font-weight: 600;
		color: #000000;
	}
@media (max-width:1000px) {
	.article-box {
		width: 100%;
		height: auto;
		padding: 10px 0 10px;
		background-color: #FFFFFF;
	}
	
	.a-title {
		width: 95%;
		line-height: 1.5;
		text-align: center;
		font-size: 23px;
		color: #000000;
		margin: 0 auto 20px;
		font-weight: 600;
	}
	
	
	

	.article-box img {
		display: block;
		width: 90% !important;
		/* height: 240px !important; */
		margin: 0 auto 20px;
		
	}

	.article-box p {
		width: 95%;
		margin: 0 auto 20px;
		
		color: #333333;
	}
	.sk-box{
		width: 90%;
		height: auto;
		padding: 15px 0;
		margin: 0 auto;
	}
	.sk-title{
		width: 90%;
		margin: 0 auto;
		height: auto;
		line-height: 2;
		font-size: 20px;
		font-weight: 600;
		color: #792c0f;
		font-family: "times new roman";
		
	}
	
}

@media (min-width:1000px) {
	.m-title{
		text-align: left;
	}
	.content{
		width: 1350px;
		display: flex;
		justify-content: space-around;
		margin: 0 auto;
	}
	.article-box {
		width: 70%;
		height: auto;
		margin: 0 auto;
		
		padding: 10px 0 10px;
		background-color: #FFFFFF;
	}
	.sticky{
		width: 28%;
		position: relative;
	}
	.list5{
		position: sticky;
		top: 5px;
		bottom: auto;
	}
	.a-title {
		width: 95%;
		line-height: 1.5;
		/* text-align: center; */
		font-size: 35px;
		color: #000000;
		margin: 0 auto 20px;
		font-weight: 600;
	}

	.article-box img {
		display: block;
		/* width: 50%;
		height: 540px; */
		margin: 0 auto 20px;
		
	}
	.article-box p {
		width: 95%;
		margin: 0 auto 20px;
		color: #333333;
	}
	.main{
		display: block !important;
	}
	.list2{
		width: 100%;
	}
	.l2-img{
		height: 250px;
	}
}
