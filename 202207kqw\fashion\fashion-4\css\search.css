body {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.main {
	flex: 1;
	position: relative;
	padding-top: 20px;
}

.m-title {
	text-transform: none;
}

.footer {
	margin-bottom: 0px !important;
}

.not_f {
	color: #000;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	font-size: 20px;
	line-height: 2;
	text-transform: capitalize;
}


@media (max-width:799px) {
	.s_title {
		width: 90%;
		margin: auto;
		font-size: 16px;
		line-height: 1.5;
		font-weight: 400;
	}
}

@media (min-width:799px) {
	.l2-box {
		width: 24%;
		height: auto;
		margin: 0 17px 25px 0px;
	}

	.l2-box:nth-of-type(4n) {
		margin-right: 0px;
	}

}