function creat(key) {
    let nodate = document.querySelector('.nodate')
    var listBox = document.querySelector(".list2")
    let value = window.location.search.split("=")[1];
    value = value.replace(/%20/g, ' ');
    function search(keyword) {
        keyword = keyword.toLowerCase();
        const result = searchList.filter(item => {
            const name = item.name.toLowerCase();
            const text = item.description.toLowerCase();
            return name.includes(keyword) || text.includes(keyword);
        });
        return result;
    }
    let newData = search(value);

    $(".m-text").innerHTML = `Searching for the keyword "${value}" results in "${newData.length}"`


    if (newData.length > 0) {
        let listLiOneBoxStr = '';
        newData.forEach((item) => {
            listLiOneBoxStr += `<div class="l2-box" onclick="location.href='${item.url}'">
                                    <div class="l2-img">
                                        <img src="${item.image_url}">
                                        <div class="m-ts">
                                            ${item.keywords}
                                        </div>
                                    </div>
									<div class="l2-text">
										${item.name}
									</div>
								</div>`
        })
        listBox.innerHTML = listLiOneBoxStr;

        nodate.style.display = "none"


    } else {

        nodate.style.display = "block"

    }
}
creat()