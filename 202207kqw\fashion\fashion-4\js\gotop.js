window.onload=function(){       
	let postionFixed = document.querySelector('.postionFixed')
	
	 if(postionFixed != null){
		    let footer = document.querySelector('.footer')
		   
		    let postHright = postionFixed.offsetHeight;
		
		    if(postHright>60) {
		        footer.style.marginBottom=postHright+'px'
		    }else{
		        footer.style.marginBottom= '60px';
		        console.log("11");
		    } 
	}
	
    var gotop=document.getElementById("gotop");                        
 
    var clientHeight=document.documentElement.clientHeight;         
 
    var timer=null;                                                 
 
    var istop=true;                                                 
    window.onscroll=function(){                                    
 
        var dtop=document.documentElement.scrollTop;                
        if(dtop >= clientHeight){
            gotop.style.display="block";
        }else{
            gotop.style.display="none";
        }
 
        if(!istop){                                                
            clearInterval(timer);
        }
        istop=false;
    }
 
    gotop.onclick=function(){                                        
 
        timer=setInterval(function(){                              
 
            var dtop=document.documentElement.scrollTop;            
 
            var speed=Math.floor(-dtop/10);                         
 
            document.documentElement.scrollTop = dtop+speed;       
                                                                   
            istop=true;                                             
 
            if(dtop==0){                                           
                clearInterval(timer);
            }
        },15)   
    }
}